<template>
  <CetDialog
    v-bind="CetDialog_addSite"
    v-on="CetDialog_addSite.event"
    class="add-site-dialog"
  >
    <div class="dialog-content bg-BG1 p-J3">
      <!-- 站点类型选择 -->
      <div class="site-type-section mb-J3">
        <div class="section-title text-T1 font-medium mb-J2">
          {{ $T("选择站点类型") }}
        </div>
        <el-select
          v-model="formData.site_type"
          :placeholder="$T('请选择站点类型')"
          size="medium"
          style="width: 100%"
          @change="handleSiteTypeChange"
        >
          <el-option
            v-for="item in recommendedSiteTypes"
            :key="item.value"
            :label="$T(item.label)"
            :value="item.value"
          />
        </el-select>

        <!-- 资源信息提示 -->
        <div
          v-if="resourceInfo"
          class="resource-info-tip text-T3 text-sm mt-J1"
        >
          {{ $T("当前资源") }}: {{ resourceInfo.name }} ({{
            $T(getResourceTypeName(resourceInfo.type))
          }})
        </div>
      </div>

      <!-- 动态表单区域 -->
      <div v-if="formData.site_type" class="form-section">
        <div class="section-title text-T1 font-medium mb-J2">
          {{ $T("站点信息") }}
        </div>

        <el-form
          ref="siteForm"
          :model="formData"
          :rules="formRules"
          label-width="120px"
          size="medium"
        >
          <!-- 基础信息 - 所有类型都有 -->
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item :label="$T('站点名称')" prop="site_name">
                <el-input
                  v-model="formData.site_name"
                  :placeholder="$T('请输入内容')"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <!-- 站点类型显示，不可编辑 -->
              <el-form-item :label="$T('站点类型')" prop="site_type_display">
                <el-input
                  :value="getSiteTypeDisplayName()"
                  :placeholder="$T('请选择')"
                  disabled
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item :label="$T('经度')" prop="longitude">
                <el-input
                  v-model="formData.longitude"
                  :placeholder="$T('请选择')"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$T('纬度')" prop="latitude">
                <el-input
                  v-model="formData.latitude"
                  :placeholder="$T('请选择')"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 储能类特有字段 -->
          <template v-if="currentGroup === 'STORAGE'">
            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item :label="$T('电压等级')" prop="voltage_level">
                  <el-select
                    v-model="formData.voltage_level"
                    :placeholder="$T('请选择')"
                    style="width: 100%"
                  >
                    <el-option label="0.4kV" value="0.4kV" />
                    <el-option label="10kV" value="10kV" />
                    <el-option label="35kV" value="35kV" />
                    <el-option label="110kV" value="110kV" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$T('并网电压')" prop="grid_voltage">
                  <el-input
                    v-model="formData.grid_voltage"
                    :placeholder="$T('请输入数值')"
                    style="width: 100%"
                  >
                    <template slot="append">kV</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item :label="$T('总装机容量')" prop="total_capacity">
                  <el-input
                    v-model="formData.total_capacity"
                    :placeholder="$T('请输入数值')"
                    style="width: 100%"
                  >
                    <template slot="append">kWh</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$T('总储电量')" prop="total_storage">
                  <el-input
                    v-model="formData.total_storage"
                    :placeholder="$T('请输入数值')"
                    style="width: 100%"
                  >
                    <template slot="append">kWh</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item :label="$T('投运时间')" prop="operation_date">
                  <el-date-picker
                    v-model="formData.operation_date"
                    type="date"
                    :placeholder="$T('请选择日期')"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$T('联系人')" prop="contact_person">
                  <el-input
                    v-model="formData.contact_person"
                    :placeholder="$T('请输入内容')"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item :label="$T('联系电话')" prop="phone_number">
                  <el-input
                    v-model="formData.phone_number"
                    :placeholder="$T('请输入内容')"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$T('关联房间')" prop="related_room">
                  <el-input
                    v-model="formData.related_room"
                    :placeholder="$T('请选择')"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="24">
                <el-form-item :label="$T('地址')" prop="site_address">
                  <el-input
                    v-model="formData.site_address"
                    :placeholder="$T('请输入内容')"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 图片上传 -->
            <el-row>
              <el-col :span="24">
                <el-form-item :label="$T('图片')" prop="images">
                  <el-upload
                    class="image-uploader"
                    action="#"
                    :show-file-list="false"
                    :before-upload="handleImageUpload"
                    accept="image/png,image/jpg,image/jpeg"
                  >
                    <div v-if="formData.imageUrl" class="image-preview">
                      <img :src="formData.imageUrl" class="uploaded-image" />
                      <div class="image-actions">
                        <i
                          class="el-icon-zoom-in"
                          @click.stop="previewImage"
                        ></i>
                        <i class="el-icon-delete" @click.stop="removeImage"></i>
                      </div>
                    </div>
                    <div v-else class="upload-placeholder">
                      <i class="el-icon-plus"></i>
                    </div>
                  </el-upload>
                  <div class="upload-tip text-T3 text-sm mt-J1">
                    {{ $T("请上传PNG、JPG、JPEG文件，大小在1M以内") }}
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </template>

          <!-- 新能源类特有字段 -->
          <template v-if="currentGroup === 'RENEWABLE'">
            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item :label="$T('电压等级')" prop="voltage_level">
                  <el-select
                    v-model="formData.voltage_level"
                    :placeholder="$T('请选择')"
                    style="width: 100%"
                  >
                    <el-option label="0.4kV" value="0.4kV" />
                    <el-option label="10kV" value="10kV" />
                    <el-option label="35kV" value="35kV" />
                    <el-option label="110kV" value="110kV" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$T('发电模式')" prop="generation_mode">
                  <el-select
                    v-model="formData.generation_mode"
                    :placeholder="$T('请选择')"
                    style="width: 100%"
                  >
                    <el-option label="自发自用" value="self_use" />
                    <el-option label="全额上网" value="full_grid" />
                    <el-option label="余电上网" value="surplus_grid" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item :label="$T('并网电压')" prop="grid_voltage">
                  <el-input
                    v-model="formData.grid_voltage"
                    :placeholder="$T('请输入数值')"
                    style="width: 100%"
                  >
                    <template slot="append">kV</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$T('总装机容量')" prop="total_capacity">
                  <el-input
                    v-model="formData.total_capacity"
                    :placeholder="$T('请输入数值')"
                    style="width: 100%"
                  >
                    <template slot="append">kWh</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item :label="$T('投运时间')" prop="operation_date">
                  <el-date-picker
                    v-model="formData.operation_date"
                    type="date"
                    :placeholder="$T('请选择日期')"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$T('联系人')" prop="contact_person">
                  <el-input
                    v-model="formData.contact_person"
                    :placeholder="$T('请输入内容')"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item :label="$T('联系电话')" prop="phone_number">
                  <el-input
                    v-model="formData.phone_number"
                    :placeholder="$T('请输入内容')"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$T('关联房间')" prop="related_room">
                  <el-input
                    v-model="formData.related_room"
                    :placeholder="$T('请选择')"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="24">
                <el-form-item :label="$T('地址')" prop="site_address">
                  <el-input
                    v-model="formData.site_address"
                    :placeholder="$T('请输入内容')"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 图片上传 -->
            <el-row>
              <el-col :span="24">
                <el-form-item :label="$T('图片')" prop="images">
                  <el-upload
                    class="image-uploader"
                    action="#"
                    :show-file-list="false"
                    :before-upload="handleImageUpload"
                    accept="image/png,image/jpg,image/jpeg"
                  >
                    <div v-if="formData.imageUrl" class="image-preview">
                      <img :src="formData.imageUrl" class="uploaded-image" />
                      <div class="image-actions">
                        <i
                          class="el-icon-zoom-in"
                          @click.stop="previewImage"
                        ></i>
                        <i class="el-icon-delete" @click.stop="removeImage"></i>
                      </div>
                    </div>
                    <div v-else class="upload-placeholder">
                      <i class="el-icon-plus"></i>
                    </div>
                  </el-upload>
                  <div class="upload-tip text-T3 text-sm mt-J1">
                    {{ $T("请上传PNG、JPG、JPEG文件，大小在1M以内") }}
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </template>

          <!-- 其他类型特有字段 -->
          <template v-if="currentGroup === 'OTHER'">
            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item :label="$T('电压等级')" prop="voltage_level">
                  <el-select
                    v-model="formData.voltage_level"
                    :placeholder="$T('请选择')"
                    style="width: 100%"
                  >
                    <el-option label="0.4kV" value="0.4kV" />
                    <el-option label="10kV" value="10kV" />
                    <el-option label="35kV" value="35kV" />
                    <el-option label="110kV" value="110kV" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$T('投运时间')" prop="operation_date">
                  <el-date-picker
                    v-model="formData.operation_date"
                    type="date"
                    :placeholder="$T('请选择日期')"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item :label="$T('联系人')" prop="contact_person">
                  <el-input
                    v-model="formData.contact_person"
                    :placeholder="$T('请输入内容')"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$T('联系电话')" prop="phone_number">
                  <el-input
                    v-model="formData.phone_number"
                    :placeholder="$T('请输入内容')"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="24">
                <el-form-item :label="$T('关联房间')" prop="related_room">
                  <el-input
                    v-model="formData.related_room"
                    :placeholder="$T('请选择')"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="24">
                <el-form-item :label="$T('地址')" prop="site_address">
                  <el-input
                    v-model="formData.site_address"
                    :placeholder="$T('请输入内容')"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 图片上传 -->
            <el-row>
              <el-col :span="24">
                <el-form-item :label="$T('图片')" prop="images">
                  <el-upload
                    class="image-uploader"
                    action="#"
                    :show-file-list="false"
                    :before-upload="handleImageUpload"
                    accept="image/png,image/jpg,image/jpeg"
                  >
                    <div v-if="formData.imageUrl" class="image-preview">
                      <img :src="formData.imageUrl" class="uploaded-image" />
                      <div class="image-actions">
                        <i
                          class="el-icon-zoom-in"
                          @click.stop="previewImage"
                        ></i>
                        <i class="el-icon-delete" @click.stop="removeImage"></i>
                      </div>
                    </div>
                    <div v-else class="upload-placeholder">
                      <i class="el-icon-plus"></i>
                    </div>
                  </el-upload>
                  <div class="upload-tip text-T3 text-sm mt-J1">
                    {{ $T("请上传PNG、JPG、JPEG文件，大小在1M以内") }}
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </template>
        </el-form>
      </div>
    </div>

    <!-- 弹窗底部按钮 -->
    <template v-slot:footer>
      <span>
        <el-button @click="handleCancel">
          {{ $T("取消") }}
        </el-button>
        <el-button type="primary" :loading="saving" @click="handleSave">
          {{ $T("确定") }}
        </el-button>
      </span>
    </template>
  </CetDialog>
</template>

<script>
import {
  SITE_TYPE_OPTIONS,
  getSiteTypeGroup,
  getRecommendedSiteTypeOptions,
  getResourceTypeName
} from "../../../../utils/siteTypes";
import { createSite } from "@/api/site-management";

export default {
  name: "AddSiteDialog",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    resourceId: {
      type: [String, Number],
      default: null
    },
    resourceInfo: {
      type: Object,
      default: () => null
    },
    vppId: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    return {
      // 弹窗配置
      CetDialog_addSite: {
        title: this.$T("新增站点"),
        openTrigger_in: 0,
        closeTrigger_in: 0,
        width: "800px",
        event: {
          openTrigger_out: this.CetDialog_addSite_openTrigger_out,
          closeTrigger_out: this.CetDialog_addSite_closeTrigger_out
        }
      },

      // 站点类型选项
      siteTypeOptions: SITE_TYPE_OPTIONS,

      // 表单数据
      formData: {
        site_type: null,
        site_name: "",
        site_address: "",
        contact_person: "",
        phone_number: "",
        longitude: "",
        latitude: "",
        // 储能类字段
        voltage_level: "",
        grid_voltage: "",
        total_capacity: "",
        total_storage: "",
        operation_date: null,
        related_room: "",
        imageUrl: "",
        // 新能源类字段
        generation_mode: "",
        // 其他类字段
        equipment_scale: "",
        service_scope: ""
      },

      // 表单验证规则
      formRules: {
        site_name: [
          {
            required: true,
            message: this.$T("请输入站点名称"),
            trigger: "blur"
          }
        ],
        site_address: [
          {
            required: true,
            message: this.$T("请输入站点地址"),
            trigger: "blur"
          }
        ],
        contact_person: [
          { required: true, message: this.$T("请输入联系人"), trigger: "blur" }
        ],
        phone_number: [
          {
            required: true,
            message: this.$T("请输入联系电话"),
            trigger: "blur"
          },
          {
            pattern: /^1[3-9]\d{9}$/,
            message: this.$T("请输入正确的手机号码"),
            trigger: "blur"
          }
        ]
      },

      // 保存状态
      saving: false
    };
  },
  computed: {
    // 当前站点类型分组
    currentGroup() {
      return this.formData.site_type
        ? getSiteTypeGroup(this.formData.site_type)
        : null;
    },

    // 根据资源类型推荐的站点类型选项
    recommendedSiteTypes() {
      if (!this.resourceInfo || !this.resourceInfo.type) {
        return this.siteTypeOptions;
      }

      // 使用新的推荐函数，基于资源类型编码
      const resourceType = this.resourceInfo.type;
      const recommendedOptions = getRecommendedSiteTypeOptions(resourceType);

      // 如果没有匹配的推荐类型，返回所有选项
      return recommendedOptions.length > 0
        ? recommendedOptions
        : this.siteTypeOptions;
    }
  },
  watch: {
    visible: {
      handler(newVal) {
        if (newVal) {
          this.CetDialog_addSite.openTrigger_in = Date.now();
        } else {
          this.CetDialog_addSite.closeTrigger_in = Date.now();
        }
      },
      immediate: true
    }
  },
  methods: {
    // 弹窗打开事件
    CetDialog_addSite_openTrigger_out() {
      this.resetForm();
    },

    // 弹窗关闭事件
    CetDialog_addSite_closeTrigger_out() {
      this.$emit("close");
    },

    // 站点类型变化处理
    handleSiteTypeChange() {
      // 清空特有字段
      // 储能类字段
      this.formData.voltage_level = "";
      this.formData.grid_voltage = "";
      this.formData.total_capacity = "";
      this.formData.total_storage = "";
      this.formData.operation_date = null;
      this.formData.related_room = "";
      this.formData.imageUrl = "";
      // 新能源类字段
      this.formData.generation_mode = "";
      // 其他类字段 - 这些字段在OTHER类型中会被使用，所以不需要清空
    },

    // 取消
    handleCancel() {
      this.$emit("close");
    },

    // 保存
    async handleSave() {
      try {
        // 表单验证
        await this.$refs.siteForm.validate();

        this.saving = true;

        // 构建保存数据 - 使用API要求的字段名（驼峰命名）
        const saveData = {
          resourceId: this.resourceId,
          vppId: this.vppId,
          siteType: this.formData.site_type,
          siteName: this.formData.site_name,
          siteAddress: this.formData.site_address,
          contactPerson: this.formData.contact_person,
          phoneNumber: this.formData.phone_number,
          longitude: this.formData.longitude,
          latitude: this.formData.latitude
        };

        // 根据站点类型添加特有字段（使用驼峰命名）
        if (this.currentGroup === "STORAGE") {
          saveData.voltageLevel = this.formData.voltage_level;
          saveData.gridVoltage = this.formData.grid_voltage;
          saveData.totalCapacity = this.formData.total_capacity;
          saveData.totalStorage = this.formData.total_storage;
          saveData.operationDate = this.formData.operation_date;
          saveData.roomId = this.formData.related_room;
          saveData.imageUrl = this.formData.imageUrl;
        } else if (this.currentGroup === "RENEWABLE") {
          saveData.voltageLevel = this.formData.voltage_level;
          saveData.generationMode = this.formData.generation_mode;
          saveData.gridVoltage = this.formData.grid_voltage;
          saveData.totalCapacity = this.formData.total_capacity;
          saveData.operationDate = this.formData.operation_date;
          saveData.roomId = this.formData.related_room;
          saveData.imageUrl = this.formData.imageUrl;
        } else if (this.currentGroup === "OTHER") {
          saveData.voltageLevel = this.formData.voltage_level;
          saveData.operationDate = this.formData.operation_date;
          saveData.roomId = this.formData.related_room;
          saveData.imageUrl = this.formData.imageUrl;
        }

        // 调用API保存站点数据
        console.log("保存站点数据:", saveData);

        const response = await createSite(saveData);

        if (response.code === 0) {
          this.$message.success(this.$T("站点创建成功"));
          this.$emit("save", response.data);
          this.$emit("close");
        } else {
          throw new Error(response.msg || "创建站点失败");
        }
      } catch (error) {
        console.error("保存站点失败:", error);
        if (error !== false) {
          // 不是表单验证错误
          this.$message.error(this.$T("保存失败，请重试"));
        }
      } finally {
        this.saving = false;
      }
    },

    // 重置表单
    resetForm() {
      this.formData = {
        site_type: null,
        site_name: "",
        site_address: "",
        contact_person: "",
        phone_number: "",
        longitude: "",
        latitude: "",
        // 储能类字段
        voltage_level: "",
        grid_voltage: "",
        total_capacity: "",
        total_storage: "",
        operation_date: null,
        related_room: "",
        imageUrl: "",
        // 新能源类字段
        installed_capacity: null,
        annual_generation: null,
        // 其他类字段
        equipment_scale: "",
        service_scope: ""
      };

      if (this.$refs.siteForm) {
        this.$refs.siteForm.clearValidate();
      }
    },

    // 获取资源类型标签（使用工具函数）
    getResourceTypeName(resourceType) {
      return getResourceTypeName(resourceType);
    },

    // 获取站点类型显示名称
    getSiteTypeDisplayName() {
      if (!this.formData.site_type) return "";
      const option = this.recommendedSiteTypes.find(
        item => item.value === this.formData.site_type
      );
      return option ? option.label : "";
    },

    // 处理图片上传
    handleImageUpload(file) {
      const isValidType = ["image/png", "image/jpg", "image/jpeg"].includes(
        file.type
      );
      const isValidSize = file.size / 1024 / 1024 < 1;

      if (!isValidType) {
        this.$message.error(this.$T("只能上传PNG、JPG、JPEG格式的图片"));
        return false;
      }
      if (!isValidSize) {
        this.$message.error(this.$T("图片大小不能超过1MB"));
        return false;
      }

      // 创建预览URL
      const reader = new FileReader();
      reader.onload = e => {
        this.formData.imageUrl = e.target.result;
      };
      reader.readAsDataURL(file);

      return false; // 阻止自动上传
    },

    // 预览图片
    previewImage() {
      // TODO: 实现图片预览功能
      console.log("预览图片:", this.formData.imageUrl);
    },

    // 删除图片
    removeImage() {
      this.formData.imageUrl = "";
    }
  }
};
</script>

<style scoped>
.add-site-dialog .dialog-content {
  max-height: 600px;
  overflow-y: auto;
}

.add-site-dialog .section-title {
  font-size: 16px;
  border-left: 4px solid var(--ZS);
  padding-left: 12px;
}

.add-site-dialog .site-type-section {
  border-bottom: 1px solid var(--BG3);
  padding-bottom: 16px;
}

.add-site-dialog .form-section {
  padding-top: 16px;
}

/* 图片上传样式 */
.image-uploader {
  display: inline-block;
}

.upload-placeholder {
  width: 120px;
  height: 120px;
  border: 2px dashed var(--BG3);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: border-color 0.3s;
}

.upload-placeholder:hover {
  border-color: var(--ZS);
}

.upload-placeholder i {
  font-size: 24px;
  color: var(--T3);
}

.image-preview {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 6px;
  overflow: hidden;
  cursor: pointer;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-actions {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  opacity: 0;
  transition: opacity 0.3s;
}

.image-preview:hover .image-actions {
  opacity: 1;
}

.image-actions i {
  color: white;
  font-size: 16px;
  cursor: pointer;
  padding: 4px;
  border-radius: 2px;
  transition: background-color 0.3s;
}

.image-actions i:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.upload-tip {
  margin-top: 8px;
  font-size: 12px;
  color: var(--T3);
}
</style>
