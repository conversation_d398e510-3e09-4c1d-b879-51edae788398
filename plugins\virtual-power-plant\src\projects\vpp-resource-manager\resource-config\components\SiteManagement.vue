<template>
  <div class="resource-management bg-BG">
    <!-- 主内容区域 -->
    <div class="rm-main-content">
      <!-- 右侧内容区域 -->
      <div class="rm-content-section bg-BG1 rounded-lg p-J3">
        <!-- 操作区 -->
        <div class="rm-toolbar flex justify-between items-center gap-J2 mb-J3">
          <div class="rm-toolbar-left flex gap-J2 items-center">
            <el-input
              v-model="searchKeyword"
              :placeholder="$T('请输入关键字')"
              prefix-icon="el-icon-search"
              size="small"
              style="width: 240px"
              @input="handleSearch"
            />
            <el-select
              v-model="selectedSiteType"
              :placeholder="$T('站点类型')"
              size="small"
              style="width: 120px"
              @change="handleSiteTypeChange"
            >
              <el-option
                v-for="item in siteTypeOptions"
                :key="item"
                :label="item"
                :value="item"
              />
            </el-select>
          </div>
          <div class="rm-toolbar-right flex gap-J1 items-center">
            <el-button
              type="danger"
              size="small"
              :disabled="selectedSites.length === 0"
              @click="handleBatchDelete"
            >
              {{ $T("批量删除") }}
            </el-button>
            <el-button type="primary" size="small" @click="handleAdd">
              {{ $T("新增") }}
            </el-button>
          </div>
        </div>

        <!-- 表格区 -->
        <div class="table-container flex-1">
          <el-table
            :data="currentPageData"
            :stripe="false"
            border
            style="width: 100%"
            class="mb-J2"
            v-loading="tableLoading"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column
              type="index"
              :label="$T('序号')"
              width="80"
              align="center"
              :index="indexMethod"
            />
            <el-table-column
              prop="siteName"
              :label="$T('站点名称')"
              show-overflow-tooltip
            />
            <el-table-column :label="$T('站点类型')" show-overflow-tooltip>
              <template slot-scope="scope">
                {{ getSiteTypeName(scope.row.siteType) }}
              </template>
            </el-table-column>
            <el-table-column
              prop="deviceCount"
              :label="$T('设备数量（个）')"
              align="center"
              width="120"
            >
              <template slot-scope="scope">
                <span class="text-ZS font-medium">
                  {{ scope.row.deviceCount }}
                </span>
              </template>
            </el-table-column>
            <el-table-column :label="$T('操作')" width="180" align="center">
              <template slot-scope="scope">
                <span
                  class="action-link detail-link"
                  @click="handleDetail(scope.row)"
                >
                  {{ $T("详情") }}
                </span>
                <span
                  class="action-link edit-link"
                  @click="handleEdit(scope.row)"
                >
                  {{ $T("编辑") }}
                </span>
                <span
                  class="action-link delete-link"
                  @click="handleDelete(scope.row)"
                >
                  {{ $T("删除") }}
                </span>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 分页区 -->
        <el-pagination
          background
          :hide-on-single-page="false"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalCount"
          class="text-right"
        />
      </div>
    </div>

    <!-- 新增站点弹窗 -->
    <AddSiteDialog
      :visible="addSiteDialogVisible"
      :resourceId="currentResourceId"
      :resourceInfo="currentResourceInfo"
      :vppId="vppId"
      @close="handleAddSiteDialogClose"
      @save="handleAddSiteDialogSave"
    />

    <!-- 站点详情面板 -->
    <SiteDetailPanel
      :visible="showDetailPanel"
      :site-data="currentSiteDetail"
      @close="showDetailPanel = false"
    />
  </div>
</template>
<script>
import AddSiteDialog from "./AddSiteDialog.vue";
import SiteDetailPanel from "./SiteDetailPanel.vue";
import {
  getSiteTypeName,
  SITE_TYPE_OPTIONS
} from "../../../../utils/siteTypes";
import {
  createSite,
  getSitePage,
  getSiteById,
  updateSite,
  deleteSitesByIds,
  countSites
} from "@/api/site-management";

export default {
  name: "SiteManagement",
  components: {
    AddSiteDialog,
    SiteDetailPanel
  },
  props: {
    node: {
      type: Object,
      default: () => null
    },
    resourceId: {
      type: Number,
      default: null
    },
    vppId: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      // 选中的站点列表
      selectedSites: [],
      // 搜索关键字
      searchKeyword: "",
      // 选中的站点类型
      selectedSiteType: "全部",
      // 分页相关
      currentPage: 1,
      pageSize: 10,
      total: 0,
      // 站点数据
      allSites: [],
      // 加载状态
      loading: false,
      tableLoading: false,
      searchTimer: null,
      // 站点类型选项
      siteTypeOptions: [
        "全部",
        ...SITE_TYPE_OPTIONS.map(option => option.label)
      ],

      // 新增站点弹窗相关
      addSiteDialogVisible: false,
      currentResourceId: null,
      currentResourceInfo: null,

      // 站点详情面板相关
      showDetailPanel: false,
      currentSiteDetail: null
    };
  },
  mounted() {
    this.loadSites();
  },
  watch: {
    searchKeyword: {
      handler() {
        this.debounceSearch();
      }
    },
    selectedSiteType: {
      handler() {
        this.loadSites();
      }
    },
    resourceId: {
      handler() {
        if (this.resourceId) {
          this.loadSites();
        }
      },
      immediate: true
    }
  },
  computed: {
    // 当前选中的节点（来自父组件props）
    selectedNode() {
      return this.node;
    },
    // 根据选中节点过滤站点
    filteredSites() {
      let filtered = this.allSites;

      // 根据选中的树节点过滤
      if (this.selectedNode) {
        const nodeType = this.selectedNode.type;
        const nodeId = this.selectedNode.tree_id;
        const originalId = this.selectedNode.originalId; // 使用原始数字ID

        console.log("🔍 过滤站点数据:", {
          nodeType,
          nodeId,
          originalId,
          allSitesCount: this.allSites.length
        });

        switch (nodeType) {
          case "user":
            // 用户节点：按用户ID过滤
            const userId =
              originalId ||
              (typeof nodeId === "string" && nodeId.startsWith("user_")
                ? Number(nodeId.replace("user_", ""))
                : nodeId);
            filtered = filtered.filter(item => item.userId === userId);
            break;
          case "resource":
            // 资源节点：API已经根据resourceId参数过滤了数据，无需前端再过滤
            console.log("🔍 资源节点选中，API已过滤数据，无需前端过滤");
            // 不进行过滤，直接使用API返回的数据
            break;
          case "site":
            // 站点节点：按站点ID过滤
            const siteId =
              originalId ||
              (typeof nodeId === "string" && nodeId.startsWith("site_")
                ? Number(nodeId.replace("site_", ""))
                : nodeId);
            filtered = filtered.filter(item => item.id === siteId);
            break;
          case "vpp":
          default:
            // 显示所有数据
            break;
        }

        console.log("🔍 过滤后的站点数据:", filtered.length, "条");
      }

      // 根据搜索关键字过滤
      if (this.searchKeyword) {
        filtered = filtered.filter(
          item =>
            item.siteName
              .toLowerCase()
              .includes(this.searchKeyword.toLowerCase()) ||
            this.getSiteTypeName(item.siteType)
              .toLowerCase()
              .includes(this.searchKeyword.toLowerCase())
        );
      }

      // 根据站点类型过滤
      if (this.selectedSiteType !== "全部") {
        // 获取选中类型对应的ID
        const selectedTypeId = this.getSiteTypeIdByName(this.selectedSiteType);
        if (selectedTypeId) {
          filtered = filtered.filter(item => item.siteType === selectedTypeId);
        }
      }

      return filtered;
    },
    // 总数 - 如果是按资源加载，使用过滤后的数据长度；否则使用API返回的total
    totalCount() {
      return this.resourceId ? this.filteredSites.length : this.total;
    },
    // 总页数
    totalPages() {
      return Math.ceil(this.totalCount / this.pageSize);
    },
    // 当前页的数据 - 使用服务端分页，直接返回API数据
    currentPageData() {
      // API已经处理了所有过滤和分页，直接返回数据
      // 只需要应用前端的搜索和站点类型过滤
      return this.filteredSites;
    }
  },
  methods: {
    // 防抖搜索
    debounceSearch() {
      clearTimeout(this.searchTimer);
      this.searchTimer = setTimeout(() => {
        this.currentPage = 1;
        this.loadSites();
      }, 500);
    },

    // 数据转换：API返回的站点数据已经是驼峰命名，直接使用
    transformSiteData(apiSite) {
      return {
        id: apiSite.id,
        siteName: apiSite.siteName,
        siteType: apiSite.siteType,
        deviceCount: apiSite.deviceCount || 0,
        siteId: apiSite.siteId,
        resourceId: apiSite.resourceId,
        roomId: apiSite.roomId,
        contactPerson: apiSite.contactPerson,
        phoneNumber: apiSite.phoneNumber,
        siteAddress: apiSite.siteAddress,
        longitude: apiSite.longitude,
        latitude: apiSite.latitude,
        createTime: apiSite.createTime,
        updateTime: apiSite.updateTime,
        // 保留原始API数据用于编辑
        originalData: apiSite
      };
    },

    // 数据转换：将组件数据转换为API发送格式(VppSiteDTO-驼峰命名)
    transformToApiFormat(siteData) {
      return {
        id: siteData.id,
        siteName: siteData.site_name,
        siteType: siteData.site_type,
        deviceCount: siteData.device_count,
        siteId: siteData.site_id,
        resourceId: siteData.resource_id,
        roomId: siteData.room_id,
        contactPerson: siteData.contact_person,
        phoneNumber: siteData.phone_number,
        siteAddress: siteData.site_address,
        longitude: siteData.longitude,
        latitude: siteData.latitude
      };
    },

    // 加载站点列表
    async loadSites() {
      this.tableLoading = true;
      try {
        // 统一使用分页查询
        const queryData = {
          pageNum: this.currentPage,
          pageSize: this.pageSize
        };

        // 添加搜索条件 - 注意SiteQueryDTO使用驼峰命名
        if (this.searchKeyword) {
          queryData.siteName = this.searchKeyword;
        }
        if (this.selectedSiteType !== "全部") {
          queryData.siteType = this.getSiteTypeIdByName(this.selectedSiteType);
        }
        if (this.vppId) {
          queryData.vppId = this.vppId;
        }
        // 如果有resourceId，添加资源过滤条件
        if (this.resourceId) {
          // 从tree_id格式中提取数字ID
          const extractedResourceId =
            typeof this.resourceId === "string" &&
            this.resourceId.startsWith("resource_")
              ? Number(this.resourceId.replace("resource_", ""))
              : Number(this.resourceId);
          queryData.resourceId = extractedResourceId;
          console.log(
            "🔍 API查询参数resourceId:",
            extractedResourceId,
            "原始值:",
            this.resourceId
          );
        }

        const response = await getSitePage(queryData);
        console.log("📊 站点列表API响应:", response);
        console.log("📊 API返回的原始records:", response.data?.records);

        if (response.code === 0) {
          // 检查原始数据
          if (response.data?.records?.length > 0) {
            console.log("📋 第一条原始站点数据:", response.data.records[0]);
          }

          this.allSites = response.data.records.map(site =>
            this.transformSiteData(site)
          );
          this.total = response.data.total;
          this.currentPage = response.data.pageNum;
          this.pageSize = response.data.pageSize;

          console.log("📋 处理后的站点数据:", this.allSites);
          console.log("📊 分页信息:", {
            total: this.total,
            currentPage: this.currentPage,
            pageSize: this.pageSize
          });
          console.log("📊 currentPageData计算结果:", this.currentPageData);
        } else {
          this.$message.error(response.msg || "加载站点列表失败");
        }
      } catch (error) {
        console.error("加载站点列表失败:", error);
        this.$message.error("加载站点列表失败");
      } finally {
        this.tableLoading = false;
      }
    },

    // 获取站点类型名称
    getSiteTypeName,

    // 根据站点类型名称获取ID
    getSiteTypeIdByName(typeName) {
      const option = SITE_TYPE_OPTIONS.find(item => item.label === typeName);
      return option ? option.value : null;
    },

    // 序号计算方法
    indexMethod(index) {
      return (this.currentPage - 1) * this.pageSize + index + 1;
    },

    // 处理搜索
    handleSearch() {
      this.currentPage = 1; // 重置到第一页
    },

    // 处理站点类型变化
    handleSiteTypeChange() {
      this.currentPage = 1; // 重置到第一页
    },

    // 处理分页大小变化
    handleSizeChange(val) {
      this.pageSize = val;
      this.currentPage = 1;
    },

    // 处理当前页变化
    handleCurrentChange(val) {
      this.currentPage = val;
    },

    // 新增
    handleAdd() {
      console.log("🔥 handleAdd 方法被调用");
      console.log("📋 当前 props.node:", this.node);
      console.log("📋 当前 selectedNode:", this.selectedNode);

      // 获取当前选中的资源信息
      if (this.selectedNode && this.selectedNode.type === "resource") {
        this.currentResourceId = this.selectedNode.tree_id;
        this.currentResourceInfo = {
          id: this.selectedNode.tree_id,
          name: this.selectedNode.name,
          type: this.selectedNode.resourceType // 资源类型编码 (1-发电, 2-储电, 3-用电, 4-微电网)
        };
        console.log("✅ 当前选中的资源信息:", this.currentResourceInfo);

        // 显示新增站点弹窗
        this.addSiteDialogVisible = true;
        console.log("✅ 弹窗状态已设置为 true");
      } else {
        console.log("❌ 未选择资源节点或节点类型不正确");
        console.log("   selectedNode:", this.selectedNode);
        console.log("   selectedNode.type:", this.selectedNode?.type);
        this.$message.warning(this.$T("请先选择一个资源节点"));
        return;
      }
    },

    // 处理选择变化
    handleSelectionChange(selection) {
      this.selectedSites = selection;
    },

    // 批量删除
    handleBatchDelete() {
      if (this.selectedSites.length === 0) {
        this.$message.warning(this.$T("请选择要删除的站点"));
        return;
      }

      this.$confirm(
        this.$T(`确定要删除选中的 ${this.selectedSites.length} 个站点吗？`),
        this.$T("批量删除确认"),
        {
          confirmButtonText: this.$T("确定"),
          cancelButtonText: this.$T("取消"),
          type: "warning"
        }
      )
        .then(() => {
          // TODO: 调用后端批量删除接口
          const siteIds = this.selectedSites.map(site => site.id);
          console.log("批量删除站点IDs:", siteIds);

          // 模拟删除成功
          this.$message.success(
            this.$T(`成功删除 ${this.selectedSites.length} 个站点`)
          );

          // 清空选中状态
          this.selectedSites = [];

          // TODO: 重新加载数据
          // this.loadSites();
        })
        .catch(() => {
          this.$message.info(this.$T("已取消删除"));
        });
    },

    // 详情
    handleDetail(item) {
      console.log("View detail:", item);

      // 设置当前站点详情数据
      this.currentSiteDetail = {
        ...item,
        // 模拟一些详细数据，实际应该从API获取
        site_address: item.site_address || "北京市朝阳区某某街道123号",
        contact_person: item.contact_person || "张三",
        phone_number: item.phone_number || "13800138000",
        longitude: item.longitude || "116.4074",
        latitude: item.latitude || "39.9042",
        voltage_level: item.voltage_level || "10kV",
        grid_voltage: item.grid_voltage || 10,
        total_capacity: item.total_capacity || 1000,
        total_storage: item.total_storage || 800,
        operation_date: item.operation_date || "2024-01-15",
        related_room: item.related_room || "1号机房",
        generation_mode: item.generation_mode || "self_use",
        imageUrl: item.imageUrl || null
      };

      // 显示详情面板
      this.showDetailPanel = true;
    },

    // 编辑
    handleEdit(item) {
      console.log("Edit item:", item);
      // TODO: 编辑逻辑
    },

    // 删除
    handleDelete(item) {
      console.log("Delete item:", item);
      // TODO: 删除逻辑
    },

    // 新增站点弹窗关闭
    handleAddSiteDialogClose() {
      this.addSiteDialogVisible = false;
      this.currentResourceId = null;
      this.currentResourceInfo = null;
    },

    // 新增站点弹窗保存
    handleAddSiteDialogSave(siteData) {
      console.log("新增站点数据:", siteData);

      // 刷新站点列表数据
      this.loadSites();

      this.$message.success(this.$T("站点新增成功"));
    }
  },
  beforeDestroy() {
    // 清理定时器
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
    }
  }
};
</script>
<style scoped>
.resource-management {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.rm-main-content {
  display: flex;
  gap: 24px;
  flex: 1;
  align-items: flex-start;
  height: 100%;
}

/* 操作链接样式 */
.action-link {
  font-size: 14px;
  cursor: pointer;
  margin-right: 16px;
  text-decoration: none;
  transition: opacity 0.2s;
}

.detail-link {
  color: var(--ZS);
}

.detail-link:hover {
  opacity: 0.8;
}

.edit-link {
  color: var(--ZS);
}

.edit-link:hover {
  opacity: 0.8;
}

.delete-link {
  color: var(--Sta3);
}

.delete-link:hover {
  opacity: 0.8;
}

.rm-tree-section {
  flex-shrink: 0;
  width: 312px;
}

.rm-content-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 24px;
  height: 100%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.table-container {
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
/* 响应式设计 */
@media (max-width: 1200px) {
  .rm-main-content {
    flex-direction: column;
    gap: 16px;
  }

  .rm-tree-section {
    width: 100%;
  }

  .rm-content-section {
    min-height: auto;
  }
}
</style>
