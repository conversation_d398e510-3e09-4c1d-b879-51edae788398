<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="14f75325-adea-4e37-b646-55eadb2f4c6e" name="Changes" comment="（feature）提交资源配置新建，查询代码">
      <change beforePath="$PROJECT_DIR$/plugins/virtual-power-plant/src/api/base-config/README.md" beforeDir="false" afterPath="$PROJECT_DIR$/plugins/virtual-power-plant/src/api/base-config/README.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/plugins/virtual-power-plant/src/api/base-config/index.js" beforeDir="false" afterPath="$PROJECT_DIR$/plugins/virtual-power-plant/src/api/base-config/index.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/plugins/virtual-power-plant/src/api/vpp-resource.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/plugins/virtual-power-plant/src/projects/vpp-resource-manager/resource-config/components/AddResourceDialog.vue" beforeDir="false" afterPath="$PROJECT_DIR$/plugins/virtual-power-plant/src/projects/vpp-resource-manager/resource-config/components/AddResourceDialog.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/plugins/virtual-power-plant/src/projects/vpp-resource-manager/resource-config/components/AddSiteDialog.vue" beforeDir="false" afterPath="$PROJECT_DIR$/plugins/virtual-power-plant/src/projects/vpp-resource-manager/resource-config/components/AddSiteDialog.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/plugins/virtual-power-plant/src/projects/vpp-resource-manager/resource-config/components/AddUserDialog.vue" beforeDir="false" afterPath="$PROJECT_DIR$/plugins/virtual-power-plant/src/projects/vpp-resource-manager/resource-config/components/AddUserDialog.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/plugins/virtual-power-plant/src/projects/vpp-resource-manager/resource-config/components/DebugTest.vue" beforeDir="false" afterPath="$PROJECT_DIR$/plugins/virtual-power-plant/src/projects/vpp-resource-manager/resource-config/components/DebugTest.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/plugins/virtual-power-plant/src/projects/vpp-resource-manager/resource-config/components/ResourceManagement.vue" beforeDir="false" afterPath="$PROJECT_DIR$/plugins/virtual-power-plant/src/projects/vpp-resource-manager/resource-config/components/ResourceManagement.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/plugins/virtual-power-plant/src/projects/vpp-resource-manager/resource-config/components/SiteManagement.vue" beforeDir="false" afterPath="$PROJECT_DIR$/plugins/virtual-power-plant/src/projects/vpp-resource-manager/resource-config/components/SiteManagement.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/plugins/virtual-power-plant/src/projects/vpp-resource-manager/resource-config/components/UserDetailDrawer.vue" beforeDir="false" afterPath="$PROJECT_DIR$/plugins/virtual-power-plant/src/projects/vpp-resource-manager/resource-config/components/UserDetailDrawer.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/plugins/virtual-power-plant/src/projects/vpp-resource-manager/resource-config/components/UserEditDialog.vue" beforeDir="false" afterPath="$PROJECT_DIR$/plugins/virtual-power-plant/src/projects/vpp-resource-manager/resource-config/components/UserEditDialog.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/plugins/virtual-power-plant/src/projects/vpp-resource-manager/resource-config/components/UserManagement.vue" beforeDir="false" afterPath="$PROJECT_DIR$/plugins/virtual-power-plant/src/projects/vpp-resource-manager/resource-config/components/UserManagement.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/plugins/virtual-power-plant/src/projects/vpp-resource-manager/resource-config/components/VppDeviceManagement.vue" beforeDir="false" afterPath="$PROJECT_DIR$/plugins/virtual-power-plant/src/projects/vpp-resource-manager/resource-config/components/VppDeviceManagement.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/plugins/virtual-power-plant/src/projects/vpp-resource-manager/resource-config/components/VppTree.vue" beforeDir="false" afterPath="$PROJECT_DIR$/plugins/virtual-power-plant/src/projects/vpp-resource-manager/resource-config/components/VppTree.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/plugins/virtual-power-plant/src/projects/vpp-resource-manager/resource-config/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/plugins/virtual-power-plant/src/projects/vpp-resource-manager/resource-config/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/plugins/virtual-power-plant/src/projects/vpp-resource-manager/resource-config/mock.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/plugins/virtual-power-plant/src/projects/vpp-resource-manager/virtual-power-plant-config/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/plugins/virtual-power-plant/src/projects/vpp-resource-manager/virtual-power-plant-config/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/plugins/virtual-power-plant/src/store/modules/vppResource.js" beforeDir="false" afterPath="$PROJECT_DIR$/plugins/virtual-power-plant/src/store/modules/vppResource.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/plugins/virtual-power-plant/vue.config.js" beforeDir="false" afterPath="$PROJECT_DIR$/plugins/virtual-power-plant/vue.config.js" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="master" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="2zZY2vMPwMnogeEQXJtVVZ87e2H" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "SONARLINT_PRECOMMIT_ANALYSIS": "true",
    "git-widget-placeholder": "develop-resource-manager",
    "last_opened_file_path": "E:/work/project/energy-fusion-web",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "ts.external.directory.path": "D:\\software\\IntelliJ IDEA 2023.3.4\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.26094.121" />
        <option value="bundled-js-predefined-d6986cc7102b-b26f3e71634d-JavaScript-IU-251.26094.121" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="14f75325-adea-4e37-b646-55eadb2f4c6e" name="Changes" comment="" />
      <created>1751939858404</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751939858404</updated>
      <workItem from="1751939859497" duration="704000" />
      <workItem from="1751941845864" duration="6035000" />
      <workItem from="1752107147960" duration="5000" />
      <workItem from="1752112566602" duration="36000" />
      <workItem from="1752113265684" duration="1000" />
      <workItem from="1752115024595" duration="3000" />
      <workItem from="1752121898823" duration="164000" />
      <workItem from="1752130638298" duration="24000" />
      <workItem from="1752130787455" duration="860000" />
      <workItem from="1752145477579" duration="999000" />
      <workItem from="1752548160452" duration="7071000" />
    </task>
    <task id="LOCAL-00001" summary="初始化代码">
      <option name="closed" value="true" />
      <created>1752038999563</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1752038999563</updated>
    </task>
    <task id="LOCAL-00002" summary="提交资源配置页面代码">
      <option name="closed" value="true" />
      <created>1752548205063</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1752548205063</updated>
    </task>
    <task id="LOCAL-00003" summary="（feature）提交资源配置新建，查询代码">
      <option name="closed" value="true" />
      <created>1752636151402</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1752636151402</updated>
    </task>
    <option name="localTasksCounter" value="4" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="初始化代码" />
    <MESSAGE value="提交资源配置页面代码" />
    <MESSAGE value="（feature）提交资源配置新建，查询代码" />
    <option name="LAST_COMMIT_MESSAGE" value="（feature）提交资源配置新建，查询代码" />
  </component>
</project>